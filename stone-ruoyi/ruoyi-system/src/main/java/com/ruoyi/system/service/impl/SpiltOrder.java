package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.AppUser;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.domain.HtOrderRecord;
import com.ruoyi.system.mapper.AppUserMapper;
import com.ruoyi.system.mapper.HtGoodsMapper;
import com.ruoyi.system.mapper.HtOrderMapper;
import com.ruoyi.system.mapper.HtOrderRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/12/6 14:49
 * @description:
 */
@Service
public class SpiltOrder {
    @Autowired
    private HtOrderMapper htOrderMapper;

    @Autowired
    private HtOrderRecordMapper htOrderRecordMapper;

    @Autowired
    private HtGoodsMapper htGoodsMapper;

    @Autowired
    private AppUserMapper appUserMapper;


    public void splitOrder() {
        // 正订单，给钱的，负订单，收钱的  石头抵消正负
        HtOrder order2 = new HtOrder();
        order2.setOrderType(0);
        order2.setOrderStatus(1L);//新加
        // 查询所有需要平台补钱的用户
        List<HtOrder> incomeList = htOrderMapper.selectHtOrderList(order2);
        // 完美匹配
        for (HtOrder incomeByOne : incomeList) {
            // 用金额和日期作为条件，用sql去匹配同一价格的值，如果有，用limit 去限制只查第一条 。进行消化。
            // payOne 是需要向平台补钱的用户，所有，你应该在谁向平台补钱的订单下，挂载支付子订单
            HtOrder payOne = htOrderMapper.selectOne(Wrappers.<HtOrder>query()
                    .eq("order_type", 1)
                    .like("created_at", DateUtils.parseDateToStr("yyyy-MM-dd", incomeByOne.getCreatedAt()))
                    .eq("order_status", 1)
                    .eq("amount", incomeByOne.getAmount())
                    .last("LIMIT 1"));
            // amount 金额不能是商品金额，应该是我昨天的收益 - 今天所购买的商品 的值
            if (payOne != null) {
                //修改sql ，2l此时状态应该是，创建订单结束，代收款
                payOne.setOrderStatus(2L);
                htOrderMapper.updateHtOrder(payOne);
                //修改sql ，2l此时状态应该是，创建订单结束，代收款
                incomeByOne.setOrderStatus(2L);
                htOrderMapper.updateHtOrder(incomeByOne);

                HtOrderRecord record = new HtOrderRecord();
                record.setCreateTime(new Date());

                record.setOrderId(payOne.getId().intValue());
                record.setAmount(payOne.getAmount());
                record.setRecipientId(incomeByOne.getUserId().intValue());
                // 这里需要查询平台需要补款的人的user信息
                AppUser appUser = appUserMapper.selectAppUserById(incomeByOne.getUserId());
                record.setRecipientName(appUser.getRealName());
                record.setRecipientPhone(appUser.getPhone());

                ///  付
                record.setUserId(payOne.getUserId().intValue());
                htOrderRecordMapper.insert(record);
            }
        }


        // 再次查询剩余，需要补款的订单，价格最大的最上，价格倒叙
        HtOrder order1 = new HtOrder();
        order1.setOrderType(0);  // 负订单
        order1.setOrderStatus(1L);
        incomeList = htOrderMapper.selectHtOrderList(order1);
        for (HtOrder incomeByOne : incomeList) {
            // 用金额和日期作为条件，用sql去匹配 对面最大的价格的值 进行消化。
            HtOrder aroundByOne = htOrderMapper.selectOne(Wrappers
                    .<HtOrder>query()
                    .eq("order_type", 1)
                    .like("created_at", DateUtils.parseDateToStr("yyyy-MM-dd", incomeByOne.getCreatedAt()))
                    .eq("order_status", 1)//todo 订单状态还是正负订单
                    .orderByDesc("amount")
                    .last("LIMIT 1"));// jiang 没有排序
            // 判断这里，是因为石头会自动拆分，造成卖家买家数量不对等。5w以上的石头，会多造成一个卖方订单，所以需要一个没有石头的用户，买下这块石头，作为补充。不然会造成订单不对等
            if (aroundByOne != null) {
                int flag = incomeByOne.getAmount().compareTo(aroundByOne.getAmount());
                // 如果我当前需要平台补钱的订单的余额，小于，给平台支付的最大订单
                if (flag <= 0) {
                    // sub == 平台最大的订单的剩余金额
                    BigDecimal sub = aroundByOne.getAmount().subtract(incomeByOne.getAmount());

                    //修改sql ，2l此时状态应该是，创建订单结束，代收款
                    incomeByOne.setOrderStatus(2L);
                    htOrderMapper.updateHtOrder(incomeByOne);

                    // 修改这条给平台付款的订单的剩余金额，重新归入统计
                    if (sub.compareTo(new BigDecimal(0)) == 0) {
                        aroundByOne.setAmount(sub);
                        aroundByOne.setOrderStatus(2L);
                        htOrderMapper.updateHtOrder(aroundByOne);
                    } else {
                        aroundByOne.setAmount(sub);
                        htOrderMapper.updateHtOrder(aroundByOne);
                    }

                    HtOrderRecord record = new HtOrderRecord();
                    record.setCreateTime(new Date());
                    record.setOrderId(aroundByOne.getId().intValue());
                    record.setAmount(incomeByOne.getAmount());
                    record.setRecipientId(incomeByOne.getUserId().intValue());
                    // 这里需要查询平台需要补款的人的user信息

                    AppUser appUser = appUserMapper.selectAppUserById(incomeByOne.getUserId());
                    record.setRecipientName(appUser.getRealName());
                    record.setRecipientPhone(appUser.getPhone());
                    ///  付
                    record.setUserId(aroundByOne.getUserId().intValue());
                    htOrderRecordMapper.insert(record);
                } else if (flag > 0) {
                    // sub == 当前没有补完钱的订单的剩余金额
                    BigDecimal sub = incomeByOne.getAmount().subtract(aroundByOne.getAmount());
                    // 此时就需要再找一个比剩余余额大的 向平台付款的订单，来消化这个 平台向他付款的订单
                    HtOrder payOne = htOrderMapper.selectOne(Wrappers
                            .<HtOrder>query()
                            .ge("amount", sub)
                            .eq("order_type", 1)
                            .ne("id", aroundByOne.getId())
                            .like("created_at", DateUtils.parseDateToStr("yyyy-MM-dd", incomeByOne.getCreatedAt()))
                            .eq("order_status", 1)//todo 订单状态还是正负订单
                            .orderByAsc("amount")
                            .last("LIMIT 1"));
                    if (payOne != null) {
                        sub = payOne.getAmount().subtract(sub);
                        //此时，消化掉了这条需要平台补额的钱
                        incomeByOne.setOrderStatus(2L);
                        htOrderMapper.updateHtOrder(incomeByOne);

                        //修改sql ，2l此时状态应该是，创建订单结束，代收款
                        aroundByOne.setOrderStatus(2L);
                        htOrderMapper.updateHtOrder(aroundByOne);

                        HtOrderRecord record = new HtOrderRecord();
                        record.setCreateTime(new Date());
                        record.setOrderId(aroundByOne.getId().intValue());
                        record.setAmount(aroundByOne.getAmount());
                        record.setRecipientId(incomeByOne.getUserId().intValue());
                        // 这里需要查询平台需要补款的人的user信息

                        AppUser appUser = appUserMapper.selectAppUserById(incomeByOne.getUserId());
                        record.setRecipientName(appUser.getRealName());
                        record.setRecipientPhone(appUser.getPhone());
                        ///  付
                        record.setUserId(aroundByOne.getUserId().intValue());
                        htOrderRecordMapper.insert(record);


                        HtOrderRecord record2 = new HtOrderRecord();
                        record2.setCreateTime(new Date());
                        record2.setOrderId(payOne.getId().intValue());
                        record2.setAmount(payOne.getAmount().subtract(sub));
                        record2.setRecipientId(incomeByOne.getUserId().intValue());
                        // 这里需要查询平台需要补款的人的user信息
                        AppUser appUser1 = appUserMapper.selectAppUserById(incomeByOne.getUserId());

                        record2.setRecipientName(appUser1.getRealName());
                        record2.setRecipientPhone(appUser1.getPhone());
                        ///  付
                        record2.setUserId(payOne.getUserId().intValue());
                        htOrderRecordMapper.insert(record2);
                        if (sub.compareTo(new BigDecimal(0)) == 0) {
                            payOne.setOrderStatus(2L);
                            htOrderMapper.updateHtOrder(payOne);
                        } else {
                            payOne.setAmount(sub);
                            htOrderMapper.updateHtOrder(payOne);
                        }


                        // 如果payOne差不到，就证明我没有比他剩下的余额更大的订单了，这时候，两个人共同付一张订单的情况就不存在了，需要更多的用户去参与消化这个订单
                    } else {
                        //如果没有payone证明要消化的订单减去around的值 剩余订单没有接近的值
                        List<HtOrder> htOrders = null;
                        if (payOne == null) {
                            // 所有小于当前订单余额的列表
                            htOrders = htOrderMapper.selectList(Wrappers.<HtOrder>query()
                                    .eq("order_status", 1)
                                    .eq("order_type", 1)
                                    .ne("id", aroundByOne.getId())
                                    .like("created_at", DateUtils.parseDateToStr("yyyy-MM-dd", incomeByOne.getCreatedAt()))
                                    .orderByAsc("amount"));
                        } else {
                            // 所有小于当前订单余额的列表
                            htOrders = htOrderMapper.selectList(Wrappers.<HtOrder>query()
                                    .eq("order_status", 1)
                                    .eq("order_type", 1)
                                    .ne("id", aroundByOne.getId())
                                    .ne("id", payOne.getId())
                                    .like("created_at", DateUtils.parseDateToStr("yyyy-MM-dd", incomeByOne.getCreatedAt()))
                                    .orderByAsc("amount"));
                        }

                        // 记录一个空值，累计有几张订单，参与了共同消化
                        BigDecimal allCount = new BigDecimal(0);
                        BigDecimal lastAmount = new BigDecimal("0");
                        // finshList 的最后一条，大概率是大于，小概率是 等于。所以跳出循环的时候，需要计算
                        List<HtOrder> finshList = new ArrayList<HtOrder>();
                        for (HtOrder ltByOne : htOrders) {
                            if (allCount.compareTo(sub) >= 0) {
                                // 如果我把多个订单累计后的值，大于我值钱剩余的值的话，我就需要计算一些，我超出的部分是多少。
                                allCount = allCount.subtract(sub);
                                break;
                            } else {
                                allCount = allCount.add(ltByOne.getAmount());
                                finshList.add(ltByOne);
                                lastAmount = ltByOne.getAmount();
                            }
                        }
                        // 走出循环的时候，就代表我取够了多条订单，凑够了当前订单的额度
                        // 这时候，判断我之前计算的剩余额度，如果等等0，证明完美抵消，如果不等于，就证明，我最后一条订单，需要拆分两次，及两次以上

                        if (allCount.compareTo(sub) >= 0) {
                            // 如果我把多个订单累计后的值，大于我值钱剩余的值的话，我就需要计算一些，我超出的部分是多少。
                            allCount = allCount.subtract(sub);
                        }
                        if (allCount.compareTo(new BigDecimal(0)) > 0) {
                            // todo  计算最后一个订单的余额，把当前的记录更新，重新排序
                            HtOrder htOrder = finshList.get(finshList.size() - 1);
                            htOrder.setAmount(allCount);
                            htOrderMapper.updateHtOrder(htOrder);
                            finshList.remove(finshList.size() - 1);
                            // 插入数据，计算少额
                            HtOrderRecord record = new HtOrderRecord();
                            record.setCreateTime(new Date());
                            record.setOrderId(htOrder.getId().intValue());
                            // 原本金额-当前金额 == 应付金额
                            record.setAmount(lastAmount.subtract(htOrder.getAmount()));
                            record.setRecipientId(incomeByOne.getUserId().intValue());
                            // 这里需要查询平台需要补款的人的user信息
                            AppUser appUser = appUserMapper.selectAppUserById(incomeByOne.getUserId());
                            record.setRecipientName(appUser.getRealName());
                            record.setRecipientPhone(appUser.getPhone());
                            ///  付
                            record.setUserId(htOrder.getUserId().intValue());
                            htOrderRecordMapper.insert(record);
                            for (HtOrder order : finshList) {
                                // 清除小订单
                                order.setOrderStatus(2L);
                                htOrderMapper.updateHtOrder(order);
                            }
                        } else {
                            for (HtOrder order : finshList) {
                                // 清除小订单
                                order.setOrderStatus(2L);
                                htOrderMapper.updateHtOrder(order);
                            }
                        }
                        HtOrderRecord htOrderRecord = new HtOrderRecord();
                        for (HtOrder order : finshList) {

                            htOrderRecord.setAmount(order.getAmount());
                            // 收款人
                            htOrderRecord.setRecipientId(incomeByOne.getUserId().intValue());
                            // 付款人
                            htOrderRecord.setUserId(order.getUserId().intValue());

                            AppUser appUser = appUserMapper.selectAppUserById(incomeByOne.getUserId());
                            htOrderRecord.setCreateTime(new Date());
                            htOrderRecord.setRecipientName(appUser.getRealName());
                            htOrderRecord.setRecipientPhone(appUser.getPhone());
                            htOrderRecord.setOrderId(order.getId().intValue());
                            htOrderRecord.setId(null);
                            htOrderRecordMapper.insert(htOrderRecord);
                        }

                        //此时，消化掉了这条需要平台补额的钱
                        incomeByOne.setOrderStatus(2L);
                        htOrderMapper.updateHtOrder(incomeByOne);

                        //修改sql ，2l此时状态应该是，创建订单结束，代收款
                        aroundByOne.setOrderStatus(2L);
                        htOrderMapper.updateHtOrder(aroundByOne);
                        // 补充记录
                        HtOrderRecord record = new HtOrderRecord();
                        record.setCreateTime(new Date());
                        record.setOrderId(aroundByOne.getId().intValue());
                        record.setAmount(aroundByOne.getAmount());
                        record.setRecipientId(incomeByOne.getUserId().intValue());
                        // 这里需要查询平台需要补款的人的user信息

                        AppUser appUser = appUserMapper.selectAppUserById(incomeByOne.getUserId());
                        record.setRecipientName(appUser.getRealName());
                        record.setRecipientPhone(appUser.getPhone());
                        ///  付
                        record.setUserId(aroundByOne.getUserId().intValue());
                        htOrderRecordMapper.insert(record);
                    }
                }
            } else {
                break;
            }
        }

        htOrderMapper.delete(Wrappers.<HtOrder>query().eq("goods_id", 0));


    }


}
