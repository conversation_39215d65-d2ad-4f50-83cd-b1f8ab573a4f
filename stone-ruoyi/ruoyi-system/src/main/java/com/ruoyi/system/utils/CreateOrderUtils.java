package com.ruoyi.system.utils;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.system.constants.BusinessConstants;
import com.ruoyi.system.domain.AppUser;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.enums.OrderStatusEnum;
import com.ruoyi.system.enums.OrderTypeEnum;
import com.ruoyi.system.mapper.AppUserMapper;
import com.ruoyi.system.mapper.HtGoodsMapper;
import com.ruoyi.system.mapper.HtOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class CreateOrderUtils {

    @Autowired
    private HtOrderMapper htOrderMapper;

    @Autowired
    private AppUserMapper appUserMapper;

    @Autowired
    private HtGoodsMapper goodsMapper;

    /**
     * 创建基础订单信息
     */
    public HtOrder createBaseOrder(Integer userId, HtGoods htGoods) {
        HtOrder htOrder = new HtOrder();
        htOrder.setGoodsId(htGoods.getId());
        htOrder.setOrderStatus(OrderStatusEnum.PENDING_REVIEW.getCode());
        htOrder.setGoodsPrice(htGoods.getPrice());
        htOrder.setRecipientId(htGoods.getUserId());
        htOrder.setUserId(Long.valueOf(userId));
        htOrder.setCreatedAt(new Date());
        htOrder.setUpdatedAt(new Date());
        htOrder.setOrderId(OrderUtils.generateOrderId(userId));

        // 设置收款人信息
        setRecipientInfo(htOrder, htGoods.getUserId());

        return htOrder;
    }

    /**
     * 设置收款人信息
     */
    private void setRecipientInfo(HtOrder htOrder, Integer recipientUserId) {
        try {
            AppUser user = appUserMapper.selectAppUserById(recipientUserId.longValue());
            if (user != null) {
                htOrder.setRecipientName(user.getRealName());
                htOrder.setRecipientPhone(user.getPhone());
            } else {
                log.warn("未找到收款人信息, userId: {}", recipientUserId);
                htOrder.setRecipientName("未知用户");
                htOrder.setRecipientPhone("");
            }
        } catch (Exception e) {
            log.error("设置收款人信息异常, userId: {}", recipientUserId, e);
            htOrder.setRecipientName("未知用户");
            htOrder.setRecipientPhone("");
        }
    }

    /**
     * 获取用户商品列表
     */
    public List<HtGoods> getUserGoodsList(Integer userId) {
        return goodsMapper.selectList(Wrappers.<HtGoods>query()
                .eq("user_id", userId)
                .eq("is_del", BusinessConstants.GOODS_NOT_DELETED));
    }

    /**
     * 计算用户商品总价值
     */
    public BigDecimal calculateUserTotalPrice(List<HtGoods> userGoodsList) {
        return userGoodsList.stream()
                .map(HtGoods::getPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 设置订单金额和类型
     */
    public void setOrderAmountAndType(HtOrder htOrder, HtGoods targetGoods,
                                       BigDecimal userTotalPrice, boolean isUserGoodsEmpty) {
        //目标商品价格
        BigDecimal targetPrice = targetGoods.getPrice();
        boolean isPlatformGoods = BusinessConstants.PLATFORM_USER_ID.equals(targetGoods.getUserId());

        // 如果用户没有商品，价格直接等于目标价格，且需要付款
        if (isUserGoodsEmpty) {
            htOrder.setAmount(targetPrice);
            htOrder.setOrderStatus(OrderStatusEnum.SPECIAL_STATUS.getCode());
            htOrder.setOrderType(OrderTypeEnum.MAKE_PAYMENT.getCode());
            return;
        }

        // 计算价格差异
        BigDecimal priceDifference = userTotalPrice.subtract(targetPrice);
        htOrder.setAmount(priceDifference.abs());

        // 根据价格比较设置订单类型
        int comparison = userTotalPrice.compareTo(targetPrice);
        OrderTypeEnum orderType = OrderTypeEnum.getByComparison(comparison);
        htOrder.setOrderType(orderType.getCode());

        // 如果价格相等，设置特殊状态
        if (comparison == 0) {
            htOrder.setOrderStatus(OrderStatusEnum.NO_PAYMENT_REQUIRED.getCode());
        }
    }
}
