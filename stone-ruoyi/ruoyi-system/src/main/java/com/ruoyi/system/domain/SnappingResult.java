package com.ruoyi.system.domain;

import lombok.Data;

/**
 * 抢购结果封装类
 * 
 * <AUTHOR>
 */
@Data
public class SnappingResult {
    private boolean success;
    private String message;
    private HtGoods goods;
    private HtOrder order;
    
    private SnappingResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    private SnappingResult(boolean success, String message, HtGoods goods, HtOrder order) {
        this.success = success;
        this.message = message;
        this.goods = goods;
        this.order = order;
    }
    
    public static SnappingResult success(HtGoods goods, HtOrder order) {
        return new SnappingResult(true, "抢购成功", goods, order);
    }
    
    public static SnappingResult fail(String message) {
        return new SnappingResult(false, message);
    }
}
